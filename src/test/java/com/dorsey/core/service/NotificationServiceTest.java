package com.dorsey.core.service;

import com.dorsey.core.controller.WebSocketController;
import com.dorsey.core.dto.users.UserNotificationConfigDTO;
import com.dorsey.core.dto.users.UserNotificationDTO;
import com.dorsey.core.model.user.UserNotificationsConfigXref;
import com.dorsey.core.model.user.UserNotificationsConfigXrefId;
import com.dorsey.core.model.user.UserNotificationsXref;
import com.dorsey.core.model.user.UserNotificationsXrefId;
import com.dorsey.core.model.user.Users;
import com.dorsey.core.repository.user.UserNotificationsConfigXrefRepo;
import com.dorsey.core.repository.user.UserNotificationsXrefRepo;
import com.dorsey.core.util.UserUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.modelmapper.ModelMapper;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Arrays;
import java.util.List;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class NotificationServiceTest {

    @Mock
    private UserNotificationsXrefRepo userNotificationsXrefRepo;

    @Mock
    private UserNotificationsConfigXrefRepo userNotificationsConfigXrefRepo;

    @Mock
    private UserUtil userUtil;

    @Mock
    private WebSocketController webSocketController;

    @Mock
    private ModelMapper modelMapper;

    @InjectMocks
    private NotificationService notificationService;

    private Users mockUser;
    private UUID userId;

    @BeforeEach
    void setUp() {
        userId = UUID.randomUUID();
        mockUser = Users.builder()
                .userId(userId)
                .email("<EMAIL>")
                .build();

        ReflectionTestUtils.setField(notificationService, "modelMapper", modelMapper);
    }

    @Test
    void testRetrieveUserNotifications() {
        // Given
        UserNotificationsXref notification1 = mock(UserNotificationsXref.class);
        UserNotificationsXref notification2 = mock(UserNotificationsXref.class);
        List<UserNotificationsXref> notifications = Arrays.asList(notification1, notification2);

        UserNotificationDTO dto1 = new UserNotificationDTO();
        UserNotificationDTO dto2 = new UserNotificationDTO();

        when(userUtil.getCurrentUser()).thenReturn(mockUser);
        when(userNotificationsXrefRepo.findByUserId(userId)).thenReturn(notifications);
        when(modelMapper.map(notification1, UserNotificationDTO.class)).thenReturn(dto1);
        when(modelMapper.map(notification2, UserNotificationDTO.class)).thenReturn(dto2);

        // When
        List<UserNotificationDTO> result = notificationService.retrieveUserNotifications();

        // Then
        assertEquals(2, result.size());
        assertTrue(result.contains(dto1));
        assertTrue(result.contains(dto2));
        verify(userNotificationsXrefRepo).findByUserId(userId);
        verify(modelMapper, times(2)).map(any(UserNotificationsXref.class), eq(UserNotificationDTO.class));
    }

    @Test
    void testCreateSingleNotification() {
        // Given
        UserNotificationsXref notification = mock(UserNotificationsXref.class);

        // When
        notificationService.createNotification(notification);

        // Then
        verify(userNotificationsXrefRepo).save(notification);
        verifyNoInteractions(webSocketController);
    }

    @Test
    void testCreateMultipleNotifications() {
        // Given
        UserNotificationsXref notification1 = mock(UserNotificationsXref.class);
        UserNotificationsXref notification2 = mock(UserNotificationsXref.class);
        List<UserNotificationsXref> notifications = Arrays.asList(notification1, notification2);

        // When
        notificationService.createNotification(notifications);

        // Then
        verify(userNotificationsXrefRepo).saveAll(notifications);
        verify(webSocketController).addNotificationCount();
    }

    @Test
    void testUpdateUserNotificationConfig() {
        // Given
        UserNotificationConfigDTO configDTO = new UserNotificationConfigDTO();
        configDTO.setType("EMAIL");
        configDTO.setObjectId("object123");
        configDTO.setNotify(true);

        when(userUtil.getCurrentUser()).thenReturn(mockUser);

        ArgumentCaptor<UserNotificationsConfigXref> configCaptor = ArgumentCaptor.forClass(UserNotificationsConfigXref.class);

        // When
        notificationService.updateUserNotificationConfig(configDTO);

        // Then
        verify(userNotificationsConfigXrefRepo).save(configCaptor.capture());
        UserNotificationsConfigXref savedConfig = configCaptor.getValue();
        assertEquals(userId, savedConfig.getId().getUserId());
        assertEquals("EMAIL", savedConfig.getId().getType());
        assertEquals("object123", savedConfig.getObjectId());
        assertTrue(savedConfig.getNotify());
    }

    @Test
    void testDeleteUserNotification() {
        // Given
        UUID testUserId = UUID.randomUUID();
        UUID notificationId = UUID.randomUUID();

        // When
        notificationService.deleteUserNotification(testUserId, notificationId);

        // Then
        ArgumentCaptor<UserNotificationsXrefId> idCaptor = ArgumentCaptor.forClass(UserNotificationsXrefId.class);
        verify(userNotificationsXrefRepo).deleteById(idCaptor.capture());
        verify(webSocketController).addNotificationCount();

        UserNotificationsXrefId deletedId = idCaptor.getValue();
        assertEquals(testUserId, deletedId.getUserId());
        assertEquals(notificationId, deletedId.getId());
    }

    @Test
    void testRetrieveUserNotificationConfig() {
        // Given
        UserNotificationsConfigXref config1 = mock(UserNotificationsConfigXref.class);
        UserNotificationsConfigXref config2 = mock(UserNotificationsConfigXref.class);
        List<UserNotificationsConfigXref> configs = Arrays.asList(config1, config2);

        UserNotificationConfigDTO dto1 = new UserNotificationConfigDTO();
        UserNotificationConfigDTO dto2 = new UserNotificationConfigDTO();

        when(userUtil.getCurrentUser()).thenReturn(mockUser);
        when(userNotificationsConfigXrefRepo.findByIdUserId(userId)).thenReturn(configs);
        when(modelMapper.map(config1, UserNotificationConfigDTO.class)).thenReturn(dto1);
        when(modelMapper.map(config2, UserNotificationConfigDTO.class)).thenReturn(dto2);

        // When
        List<UserNotificationConfigDTO> result = notificationService.retrieveUserNotificationConfig();

        // Then
        assertEquals(2, result.size());
        assertTrue(result.contains(dto1));
        assertTrue(result.contains(dto2));
        verify(userNotificationsConfigXrefRepo).findByIdUserId(userId);
        verify(modelMapper, times(2)).map(any(UserNotificationsConfigXref.class), eq(UserNotificationConfigDTO.class));
    }
}
