package com.dorsey.core.service.organization;

import com.dorsey.core.dto.organization.HierarchyLevelDTO;
import com.dorsey.core.model.organization.HierarchyLevel;
import com.dorsey.core.model.organization.HierarchyNode;
import com.dorsey.core.model.organization.Item;
import com.dorsey.core.model.user.Users;
import com.dorsey.core.repository.organization.HierarchyLevelRepo;
import com.dorsey.core.repository.organization.HierarchyNodeRepo;
import com.dorsey.core.repository.organization.ItemRepo;
import com.dorsey.core.service.user.UserService;
import com.dorsey.core.util.UserUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.modelmapper.ModelMapper;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Arrays;
import java.util.List;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class OrganizationServiceTest {

    @Mock
    private HierarchyLevelRepo hierarchyLevelRepo;

    @Mock
    private HierarchyNodeRepo hierarchyNodeRepo;

    @Mock
    private UserUtil userUtil;

    @Mock
    private UserService userService;

    @Mock
    private ItemRepo hierarchyItemRepo;

    @Mock
    private ModelMapper modelMapper;

    @InjectMocks
    private OrganizationService organizationService;

    private HierarchyLevel level1;
    private HierarchyLevel level2;
    private HierarchyNode node1;
    private HierarchyNode node2;
    private HierarchyLevelDTO levelDTO1;
    private HierarchyLevelDTO levelDTO2;

    @BeforeEach
    void setUp() {
        level1 = HierarchyLevel.builder()
                .level(1)
                .hierarchyName("Department")
                .description("Department Level")
                .build();

        level2 = HierarchyLevel.builder()
                .level(2)
                .hierarchyName("Team")
                .description("Team Level")
                .build();

        node1 = HierarchyNode.builder()
                .nodeId(1)
                .level(1)
                .hierarchyValue("Engineering")
                .build();

        node2 = HierarchyNode.builder()
                .nodeId(2)
                .level(2)
                .hierarchyValue("Backend Team")
                .build();

        levelDTO1 = HierarchyLevelDTO.builder()
                .level(1)
                .hierarchyName("Department")
                .description("Department Level")
                .inUse(false)
                .build();

        levelDTO2 = HierarchyLevelDTO.builder()
                .level(2)
                .hierarchyName("Team")
                .description("Team Level")
                .inUse(false)
                .build();

        ReflectionTestUtils.setField(organizationService, "modelMapper", modelMapper);
    }

    @Test
    void testRetrieveAllLevels() {
        // Given
        List<HierarchyLevel> levels = Arrays.asList(level1, level2);
        List<HierarchyNode> nodes = Arrays.asList(node1, node2);

        when(hierarchyLevelRepo.findAll()).thenReturn(levels);
        when(hierarchyNodeRepo.findAll()).thenReturn(nodes);
        when(modelMapper.map(level1, HierarchyLevelDTO.class)).thenReturn(levelDTO1);
        when(modelMapper.map(level2, HierarchyLevelDTO.class)).thenReturn(levelDTO2);

        // When
        List<HierarchyLevelDTO> result = organizationService.retrieveAllLevels();

        // Then
        assertEquals(2, result.size());
        
        // Verify that inUse is set correctly
        HierarchyLevelDTO resultLevel1 = result.stream()
                .filter(dto -> dto.getLevel() == 1)
                .findFirst()
                .orElse(null);
        assertNotNull(resultLevel1);
        assertTrue(resultLevel1.getInUse()); // Should be true because node1 has level 1

        HierarchyLevelDTO resultLevel2 = result.stream()
                .filter(dto -> dto.getLevel() == 2)
                .findFirst()
                .orElse(null);
        assertNotNull(resultLevel2);
        assertTrue(resultLevel2.getInUse()); // Should be true because node2 has level 2

        verify(hierarchyLevelRepo).findAll();
        verify(hierarchyNodeRepo).findAll();
        verify(modelMapper).map(level1, HierarchyLevelDTO.class);
        verify(modelMapper).map(level2, HierarchyLevelDTO.class);
    }

    @Test
    void testRetrieveAllLevelsWithNoNodes() {
        // Given
        List<HierarchyLevel> levels = Arrays.asList(level1, level2);
        List<HierarchyNode> emptyNodes = Arrays.asList();

        when(hierarchyLevelRepo.findAll()).thenReturn(levels);
        when(hierarchyNodeRepo.findAll()).thenReturn(emptyNodes);
        when(modelMapper.map(level1, HierarchyLevelDTO.class)).thenReturn(levelDTO1);
        when(modelMapper.map(level2, HierarchyLevelDTO.class)).thenReturn(levelDTO2);

        // When
        List<HierarchyLevelDTO> result = organizationService.retrieveAllLevels();

        // Then
        assertEquals(2, result.size());
        
        // Verify that inUse is false for all levels when no nodes exist
        result.forEach(dto -> assertFalse(dto.getInUse()));

        verify(hierarchyLevelRepo).findAll();
        verify(hierarchyNodeRepo).findAll();
        verify(modelMapper).map(level1, HierarchyLevelDTO.class);
        verify(modelMapper).map(level2, HierarchyLevelDTO.class);
    }

    @Test
    void testRetrieveAllLevelsWithPartialNodeUsage() {
        // Given
        List<HierarchyLevel> levels = Arrays.asList(level1, level2);
        List<HierarchyNode> nodes = Arrays.asList(node1); // Only node1 with level 1

        when(hierarchyLevelRepo.findAll()).thenReturn(levels);
        when(hierarchyNodeRepo.findAll()).thenReturn(nodes);
        when(modelMapper.map(level1, HierarchyLevelDTO.class)).thenReturn(levelDTO1);
        when(modelMapper.map(level2, HierarchyLevelDTO.class)).thenReturn(levelDTO2);

        // When
        List<HierarchyLevelDTO> result = organizationService.retrieveAllLevels();

        // Then
        assertEquals(2, result.size());
        
        // Verify that only level 1 is marked as in use
        HierarchyLevelDTO resultLevel1 = result.stream()
                .filter(dto -> dto.getLevel() == 1)
                .findFirst()
                .orElse(null);
        assertNotNull(resultLevel1);
        assertTrue(resultLevel1.getInUse());

        HierarchyLevelDTO resultLevel2 = result.stream()
                .filter(dto -> dto.getLevel() == 2)
                .findFirst()
                .orElse(null);
        assertNotNull(resultLevel2);
        assertFalse(resultLevel2.getInUse());

        verify(hierarchyLevelRepo).findAll();
        verify(hierarchyNodeRepo).findAll();
        verify(modelMapper).map(level1, HierarchyLevelDTO.class);
        verify(modelMapper).map(level2, HierarchyLevelDTO.class);
    }

    @Test
    void testRetrieveAllLevelsEmptyLevels() {
        // Given
        List<HierarchyLevel> emptyLevels = Arrays.asList();
        List<HierarchyNode> nodes = Arrays.asList(node1, node2);

        when(hierarchyLevelRepo.findAll()).thenReturn(emptyLevels);
        when(hierarchyNodeRepo.findAll()).thenReturn(nodes);

        // When
        List<HierarchyLevelDTO> result = organizationService.retrieveAllLevels();

        // Then
        assertTrue(result.isEmpty());

        verify(hierarchyLevelRepo).findAll();
        verify(hierarchyNodeRepo).findAll();
        verifyNoInteractions(modelMapper);
    }

    @Test
    void testRetrieveAllLevelsWithNullLevels() {
        // Given
        when(hierarchyLevelRepo.findAll()).thenReturn(null);
        when(hierarchyNodeRepo.findAll()).thenReturn(Arrays.asList());

        // When & Then
        assertThrows(NullPointerException.class, 
            () -> organizationService.retrieveAllLevels());

        verify(hierarchyLevelRepo).findAll();
        verify(hierarchyNodeRepo).findAll();
    }

    @Test
    void testRetrieveAllLevelsWithNullNodes() {
        // Given
        List<HierarchyLevel> levels = Arrays.asList(level1, level2);
        when(hierarchyLevelRepo.findAll()).thenReturn(levels);
        when(hierarchyNodeRepo.findAll()).thenReturn(null);
        when(modelMapper.map(level1, HierarchyLevelDTO.class)).thenReturn(levelDTO1);
        when(modelMapper.map(level2, HierarchyLevelDTO.class)).thenReturn(levelDTO2);

        // When & Then
        assertThrows(NullPointerException.class, 
            () -> organizationService.retrieveAllLevels());

        verify(hierarchyLevelRepo).findAll();
        verify(hierarchyNodeRepo).findAll();
    }
}
