package com.dorsey.core.service.dataload;

import com.dorsey.core.dto.dataload.UploadedDataDTO;
import com.dorsey.core.exception.NotFoundException;
import com.dorsey.core.model.dataload.UploadedData;
import com.dorsey.core.model.lov.DataSetLov;
import com.dorsey.core.model.lov.UploadedDataStatusLov;
import com.dorsey.core.model.user.Users;
import com.dorsey.core.repository.audit.AuditLogRepo;
import com.dorsey.core.repository.dataload.UploadedDataRepo;
import com.dorsey.core.repository.dataload.UploadedDataStatusLovRepo;
import com.dorsey.core.repository.file.FileRepository;
import com.dorsey.core.repository.lov.DataSetLovRepo;
import com.dorsey.core.util.UserUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.modelmapper.ModelMapper;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class UploadedDataServiceTest {

    @Mock
    private UploadedDataRepo uploadedDataRepo;

    @Mock
    private UploadedDataStatusLovRepo uploadedDataStatusLovRepo;

    @Mock
    private DataSetLovRepo dataSetLovRepo;

    @Mock
    private FileRepository fileRepository;

    @Mock
    private CoreFileProcessorService fileProcessorService;

    @Mock
    private AuditLogRepo auditLogRepo;

    @Mock
    private UserUtil userUtil;

    @Mock
    private ModelMapper modelMapper;

    @Mock
    private MultipartFile multipartFile;

    @InjectMocks
    private UploadedDataService uploadedDataService;

    private Users mockUser;
    private UUID userId;
    private UploadedData uploadedData;
    private UploadedDataDTO uploadedDataDTO;
    private DataSetLov dataSetLov;
    private UploadedDataStatusLov statusLov;

    @BeforeEach
    void setUp() {
        userId = UUID.randomUUID();
        mockUser = Users.builder()
                .userId(userId)
                .email("<EMAIL>")
                .build();

        uploadedData = UploadedData.builder()
                .dataSetCode("TEST")
                .fileName("test.xlsx")
                .statusCode("PENDING")
                .uploadedDate(LocalDateTime.now())
                .build();

        uploadedDataDTO = UploadedDataDTO.builder()
                .dataSetCode("TEST")
                .fileName("test.xlsx")
                .statusCode("PENDING")
                .build();

        dataSetLov = DataSetLov.builder()
                .code("TEST")
                .description("Test Dataset")
                .build();

        statusLov = UploadedDataStatusLov.builder()
                .code("PENDING")
                .description("Pending")
                .build();

        ReflectionTestUtils.setField(uploadedDataService, "modelMapper", modelMapper);
    }

    @Test
    void testRetrieveUploadedData() {
        // Given
        List<UploadedData> uploadedDataList = Arrays.asList(uploadedData);
        List<DataSetLov> dataSetLovs = Arrays.asList(dataSetLov);
        List<UploadedDataStatusLov> statusLovs = Arrays.asList(statusLov);

        when(uploadedDataRepo.findAllExceptLogo()).thenReturn(uploadedDataList);
        when(dataSetLovRepo.findAll()).thenReturn(dataSetLovs);
        when(uploadedDataStatusLovRepo.findAll()).thenReturn(statusLovs);
        when(modelMapper.map(uploadedData, UploadedDataDTO.class)).thenReturn(uploadedDataDTO);

        // When
        List<UploadedDataDTO> result = uploadedDataService.retrieveUploadedData();

        // Then
        assertEquals(1, result.size());
        verify(uploadedDataRepo).findAllExceptLogo();
        verify(dataSetLovRepo).findAll();
        verify(uploadedDataStatusLovRepo).findAll();
        verify(modelMapper).map(uploadedData, UploadedDataDTO.class);
    }

    @Test
    void testGetFile() {
        // Given
        String uploadId = "test-id";
        uploadedData.setDataSetCode("TEST");
        uploadedData.setFileName("test.xlsx");

        when(uploadedDataRepo.findById(uploadId)).thenReturn(Optional.of(uploadedData));
        when(fileRepository.createPath("TEST", "test.xlsx")).thenReturn(java.nio.file.Paths.get("test.xlsx"));

        // When
        Optional<java.io.File> result = uploadedDataService.getFile(uploadId);

        // Then
        assertTrue(result.isPresent());
        verify(uploadedDataRepo).findById(uploadId);
        verify(fileRepository).createPath("TEST", "test.xlsx");
    }

    @Test
    void testGetFileNotFound() {
        // Given
        String nonExistentId = "non-existent";
        when(uploadedDataRepo.findById(nonExistentId)).thenReturn(Optional.empty());

        // When
        Optional<java.io.File> result = uploadedDataService.getFile(nonExistentId);

        // Then
        assertFalse(result.isPresent());
        verify(uploadedDataRepo).findById(nonExistentId);
        verifyNoInteractions(fileRepository);
    }

    @Test
    void testUpdateUploadedData() {
        // Given
        uploadedDataDTO.setDataSetCode("TEST");
        uploadedDataDTO.setStatus("Pending");

        when(dataSetLovRepo.findById("TEST")).thenReturn(Optional.of(dataSetLov));
        when(uploadedDataStatusLovRepo.findByDescription("Pending")).thenReturn(Optional.of(statusLov));
        when(fileRepository.saveFile("TEST", multipartFile)).thenReturn("test.xlsx");
        when(userUtil.getCurrentUser()).thenReturn(mockUser);
        when(uploadedDataRepo.save(any(UploadedData.class))).thenReturn(uploadedData);

        // When
        UploadedDataDTO result = uploadedDataService.updateUploadedData(fileProcessorService, uploadedDataDTO, multipartFile);

        // Then
        assertEquals(uploadedDataDTO, result);
        verify(dataSetLovRepo).findById("TEST");
        verify(uploadedDataStatusLovRepo).findByDescription("Pending");
        verify(fileRepository).saveFile("TEST", multipartFile);
        verify(fileProcessorService).process(dataSetLov, multipartFile);
        verify(auditLogRepo).save(any());
        verify(uploadedDataRepo).save(any(UploadedData.class));
    }

    @Test
    void testUpdateUploadedDataWithInvalidDataSet() {
        // Given
        uploadedDataDTO.setDataSetCode("INVALID");
        when(dataSetLovRepo.findById("INVALID")).thenReturn(Optional.empty());

        // When & Then
        assertThrows(NotFoundException.class,
            () -> uploadedDataService.updateUploadedData(fileProcessorService, uploadedDataDTO, multipartFile));

        verify(dataSetLovRepo).findById("INVALID");
        verifyNoInteractions(uploadedDataRepo, fileProcessorService);
    }

    @Test
    void testUpdateUploadedDataWithInvalidStatus() {
        // Given
        uploadedDataDTO.setDataSetCode("TEST");
        uploadedDataDTO.setStatus("InvalidStatus");

        when(dataSetLovRepo.findById("TEST")).thenReturn(Optional.of(dataSetLov));
        when(uploadedDataStatusLovRepo.findByDescription("InvalidStatus")).thenReturn(Optional.empty());

        // When & Then
        assertThrows(NotFoundException.class,
            () -> uploadedDataService.updateUploadedData(fileProcessorService, uploadedDataDTO, multipartFile));

        verify(dataSetLovRepo).findById("TEST");
        verify(uploadedDataStatusLovRepo).findByDescription("InvalidStatus");
        verifyNoInteractions(uploadedDataRepo, fileProcessorService);
    }

    @Test
    void testUpdateUploadedDataWithNullDTO() {
        // When & Then
        assertThrows(IllegalArgumentException.class,
            () -> uploadedDataService.updateUploadedData(fileProcessorService, null, multipartFile));

        verifyNoInteractions(dataSetLovRepo, uploadedDataRepo, fileProcessorService);
    }

    @Test
    void testUpdateUploadedDataWithNullFile() {
        // When & Then
        assertThrows(IllegalArgumentException.class,
            () -> uploadedDataService.updateUploadedData(fileProcessorService, uploadedDataDTO, null));

        verifyNoInteractions(dataSetLovRepo, uploadedDataRepo, fileProcessorService);
    }

    @Test
    void testUpdateUploadedDataWithNullDataSetCode() {
        // Given
        uploadedDataDTO.setDataSetCode(null);

        // When & Then
        assertThrows(IllegalArgumentException.class,
            () -> uploadedDataService.updateUploadedData(fileProcessorService, uploadedDataDTO, multipartFile));

        verifyNoInteractions(dataSetLovRepo, uploadedDataRepo, fileProcessorService);
    }
}
