package com.dorsey.core.service;

import com.dorsey.core.enums.AuditAction;
import com.dorsey.core.model.AbstractModel;
import com.dorsey.core.model.logging.AuditLog;
import com.dorsey.core.model.user.Users;
import com.dorsey.core.repository.audit.AuditLogRepo;
import com.dorsey.core.util.UserUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class AuditListenerTest {

    @Mock
    private AuditLogRepo auditLogRepo;

    @Mock
    private UserUtil userUtil;

    private AuditListener auditListener;

    @BeforeEach
    void setUp() {
        auditListener = new AuditListener();
        auditListener.init(auditLogRepo, userUtil);
    }

    @Test
    void testInit() {
        // Given
        AuditLogRepo newRepo = mock(AuditLogRepo.class);
        UserUtil newUserUtil = mock(UserUtil.class);

        // When
        auditListener.init(newRepo, newUserUtil);

        // Then
        assertDoesNotThrow(() -> auditListener.init(newRepo, newUserUtil));
    }

    @Test
    void testDoCreate() throws Exception {
        // Given
        Object entity = new TestEntity();
        ArgumentCaptor<AuditLog> auditLogCaptor = ArgumentCaptor.forClass(AuditLog.class);

        // Mock the buildAuditLog method behavior
        try (MockedStatic<AuditListener> mockedStatic = mockStatic(AuditListener.class, CALLS_REAL_METHODS)) {
            AuditLog expectedAuditLog = mock(AuditLog.class);
            mockedStatic.when(() -> AuditListener.buildAuditLog(AuditAction.CREATE, entity))
                    .thenReturn(expectedAuditLog);

            // When
            ReflectionTestUtils.invokeMethod(auditListener, "doCreate", entity);

            // Then
            verify(auditLogRepo).save(auditLogCaptor.capture());
            assertEquals(expectedAuditLog, auditLogCaptor.getValue());
        }
    }

    @Test
    void testDoUpdate() throws Exception {
        // Given
        Object entity = new TestEntity();
        ArgumentCaptor<AuditLog> auditLogCaptor = ArgumentCaptor.forClass(AuditLog.class);

        // Mock the buildAuditLog method behavior
        try (MockedStatic<AuditListener> mockedStatic = mockStatic(AuditListener.class, CALLS_REAL_METHODS)) {
            AuditLog expectedAuditLog = mock(AuditLog.class);
            mockedStatic.when(() -> AuditListener.buildAuditLog(AuditAction.UPDATE, entity))
                    .thenReturn(expectedAuditLog);

            // When
            ReflectionTestUtils.invokeMethod(auditListener, "doUpdate", entity);

            // Then
            verify(auditLogRepo).save(auditLogCaptor.capture());
            assertEquals(expectedAuditLog, auditLogCaptor.getValue());
        }
    }

    @Test
    void testDoDelete() throws Exception {
        // Given
        Object entity = new TestEntity();
        ArgumentCaptor<AuditLog> auditLogCaptor = ArgumentCaptor.forClass(AuditLog.class);

        // Mock the buildAuditLog method behavior
        try (MockedStatic<AuditListener> mockedStatic = mockStatic(AuditListener.class, CALLS_REAL_METHODS)) {
            AuditLog expectedAuditLog = mock(AuditLog.class);
            mockedStatic.when(() -> AuditListener.buildAuditLog(AuditAction.DELETE, entity))
                    .thenReturn(expectedAuditLog);

            // When
            ReflectionTestUtils.invokeMethod(auditListener, "doDelete", entity);

            // Then
            verify(auditLogRepo).save(auditLogCaptor.capture());
            assertEquals(expectedAuditLog, auditLogCaptor.getValue());
        }
    }

    // Test entity class for testing purposes
    private static class TestEntity {
        private String name = "test";
        
        public String getName() {
            return name;
        }
        
        public void setName(String name) {
            this.name = name;
        }
    }
}
