package com.dorsey.core.service.system;

import com.dorsey.core.dto.system.SystemDTO;
import com.dorsey.core.model.system.System;
import com.dorsey.core.model.lov.SystemFilesLov;
import com.dorsey.core.repository.system.SystemRepo;
import com.dorsey.core.repository.lov.SystemFilesLovRepo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.modelmapper.ModelMapper;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class SystemServiceTest {

    @Mock
    private SystemRepo systemRepo;

    @Mock
    private SystemFilesLovRepo systemFileLovRepo;

    @Mock
    private ModelMapper modelMapper;

    @InjectMocks
    private SystemService systemService;

    private System systemEntity;
    private SystemDTO systemDTO;
    private SystemFilesLov systemFile1;
    private SystemFilesLov systemFile2;

    @BeforeEach
    void setUp() {
        systemEntity = System.builder()
                .id("system-config")
                .theme("dark")
                .colors("blue,green,red")
                .logo("logo.png")
                .build();

        systemDTO = SystemDTO.builder()
                .theme("dark")
                .colors(Arrays.asList("blue", "green", "red"))
                .logo("logo.png")
                .build();

        systemFile1 = SystemFilesLov.builder()
                .code("FILE1")
                .description("File 1")
                .fileName("file1.txt")
                .build();

        systemFile2 = SystemFilesLov.builder()
                .code("FILE2")
                .description("File 2")
                .fileName("file2.txt")
                .build();

        ReflectionTestUtils.setField(systemService, "modelMapper", modelMapper);
    }

    @Test
    void testRetrieveConfiguration() {
        // Given
        when(systemRepo.findById("system-config")).thenReturn(Optional.of(systemEntity));

        // When
        SystemDTO result = systemService.retrieveConfiguration();

        // Then
        assertNotNull(result);
        assertEquals("dark", result.getTheme());
        assertEquals(Arrays.asList("blue", "green", "red"), result.getColors());
        assertEquals("logo.png", result.getLogo());
        verify(systemRepo).findById("system-config");
    }

    @Test
    void testRetrieveConfigurationNotFound() {
        // Given
        when(systemRepo.findById("system-config")).thenReturn(Optional.empty());

        // When
        SystemDTO result = systemService.retrieveConfiguration();

        // Then
        assertNull(result);
        verify(systemRepo).findById("system-config");
    }

    @Test
    void testRetrieveConfigurationWithNullColors() {
        // Given
        System systemWithNullColors = System.builder()
                .id("system-config")
                .theme("light")
                .colors(null)
                .logo("logo.png")
                .build();
        
        when(systemRepo.findById("system-config")).thenReturn(Optional.of(systemWithNullColors));

        // When
        SystemDTO result = systemService.retrieveConfiguration();

        // Then
        assertNotNull(result);
        assertEquals("light", result.getTheme());
        assertTrue(result.getColors().isEmpty());
        assertEquals("logo.png", result.getLogo());
        verify(systemRepo).findById("system-config");
    }

    @Test
    void testRetrieveConfigurationWithEmptyColors() {
        // Given
        System systemWithEmptyColors = System.builder()
                .id("system-config")
                .theme("light")
                .colors("")
                .logo("logo.png")
                .build();
        
        when(systemRepo.findById("system-config")).thenReturn(Optional.of(systemWithEmptyColors));

        // When
        SystemDTO result = systemService.retrieveConfiguration();

        // Then
        assertNotNull(result);
        assertEquals("light", result.getTheme());
        assertEquals(Arrays.asList(""), result.getColors()); // Empty string becomes single empty element
        assertEquals("logo.png", result.getLogo());
        verify(systemRepo).findById("system-config");
    }

    @Test
    void testRetrieveConfigurationWithSingleColor() {
        // Given
        System systemWithSingleColor = System.builder()
                .id("system-config")
                .theme("light")
                .colors("blue")
                .logo("logo.png")
                .build();
        
        when(systemRepo.findById("system-config")).thenReturn(Optional.of(systemWithSingleColor));

        // When
        SystemDTO result = systemService.retrieveConfiguration();

        // Then
        assertNotNull(result);
        assertEquals("light", result.getTheme());
        assertEquals(Arrays.asList("blue"), result.getColors());
        assertEquals("logo.png", result.getLogo());
        verify(systemRepo).findById("system-config");
    }

    @Test
    void testRetrieveConfigurationWithTrailingCommas() {
        // Given
        System systemWithTrailingCommas = System.builder()
                .id("system-config")
                .theme("light")
                .colors("blue,green,red,")
                .logo("logo.png")
                .build();
        
        when(systemRepo.findById("system-config")).thenReturn(Optional.of(systemWithTrailingCommas));

        // When
        SystemDTO result = systemService.retrieveConfiguration();

        // Then
        assertNotNull(result);
        assertEquals("light", result.getTheme());
        assertEquals(Arrays.asList("blue", "green", "red", ""), result.getColors());
        assertEquals("logo.png", result.getLogo());
        verify(systemRepo).findById("system-config");
    }

    @Test
    void testRetrieveAllSystemFiles() {
        // Given
        List<SystemFilesLov> systemFiles = Arrays.asList(systemFile1, systemFile2);
        when(systemFileLovRepo.findAll()).thenReturn(systemFiles);

        // When
        List<SystemFilesLov> result = systemService.retrieveAllSystemFiles();

        // Then
        assertEquals(2, result.size());
        assertTrue(result.contains(systemFile1));
        assertTrue(result.contains(systemFile2));
        verify(systemFileLovRepo).findAll();
    }

    @Test
    void testRetrieveAllSystemFilesEmpty() {
        // Given
        when(systemFileLovRepo.findAll()).thenReturn(Arrays.asList());

        // When
        List<SystemFilesLov> result = systemService.retrieveAllSystemFiles();

        // Then
        assertTrue(result.isEmpty());
        verify(systemFileLovRepo).findAll();
    }

    @Test
    void testUpdateSystemConfiguration() {
        // Given
        when(systemRepo.save(any(System.class))).thenReturn(systemEntity);

        // When
        SystemDTO result = systemService.updateSystemConfiguration(systemDTO);

        // Then
        assertNotNull(result);
        assertEquals(systemDTO.getTheme(), result.getTheme());
        assertEquals(systemDTO.getColors(), result.getColors());
        assertEquals(systemDTO.getLogo(), result.getLogo());
        verify(systemRepo).save(any(System.class));
    }

    @Test
    void testUpdateSystemConfigurationWithNullColors() {
        // Given
        SystemDTO dtoWithNullColors = SystemDTO.builder()
                .theme("light")
                .colors(null)
                .logo("logo.png")
                .build();
        
        when(systemRepo.save(any(System.class))).thenReturn(systemEntity);

        // When
        SystemDTO result = systemService.updateSystemConfiguration(dtoWithNullColors);

        // Then
        assertNotNull(result);
        verify(systemRepo).save(any(System.class));
    }

    @Test
    void testUpdateSystemConfigurationWithEmptyColors() {
        // Given
        SystemDTO dtoWithEmptyColors = SystemDTO.builder()
                .theme("light")
                .colors(Arrays.asList())
                .logo("logo.png")
                .build();
        
        when(systemRepo.save(any(System.class))).thenReturn(systemEntity);

        // When
        SystemDTO result = systemService.updateSystemConfiguration(dtoWithEmptyColors);

        // Then
        assertNotNull(result);
        verify(systemRepo).save(any(System.class));
    }
}
