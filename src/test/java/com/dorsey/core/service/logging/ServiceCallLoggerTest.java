package com.dorsey.core.service.logging;

import com.dorsey.core.annotation.NoLogResponse;
import com.dorsey.core.dto.Message;
import com.dorsey.core.enums.MessageLevel;
import com.dorsey.core.exception.AuthorizationException;
import com.dorsey.core.exception.ResourceException;
import com.dorsey.core.exception.ServiceException;
import com.dorsey.core.model.logging.ServiceCallLog;
import com.dorsey.core.model.user.Users;
import com.dorsey.core.util.UserUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.util.ContentCachingRequestWrapper;
import org.springframework.web.util.ContentCachingResponseWrapper;

import java.lang.reflect.Method;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ServiceCallLoggerTest {

    @Mock
    private LoggingService loggingService;

    @Mock
    private UserUtil userUtil;

    @Mock
    private ContentCachingRequestWrapper request;

    @Mock
    private ContentCachingResponseWrapper response;

    @InjectMocks
    private ServiceCallLogger serviceCallLogger;

    private Users mockUser;
    private UUID userId;

    @BeforeEach
    void setUp() {
        userId = UUID.randomUUID();
        mockUser = Users.builder()
                .userId(userId)
                .email("<EMAIL>")
                .build();
    }

    @Test
    void testInit() {
        // When
        serviceCallLogger.init();

        // Then
        // Verify that a new log entry is created and initialized
        assertDoesNotThrow(() -> serviceCallLogger.init());
    }

    @Test
    void testGetRESTParametersWithoutNoLogResponse() throws NoSuchMethodException {
        // Given
        String className = "TestController";
        Method method = TestController.class.getMethod("testMethod");
        
        serviceCallLogger.init();

        // When
        serviceCallLogger.getRESTParameters(className, method);

        // Then
        // Verify method was called without exceptions
        assertDoesNotThrow(() -> serviceCallLogger.getRESTParameters(className, method));
    }

    @Test
    void testGetRESTParametersWithNoLogResponse() throws NoSuchMethodException {
        // Given
        String className = "TestController";
        Method method = TestController.class.getMethod("testMethodWithNoLogResponse");
        
        serviceCallLogger.init();

        // When
        serviceCallLogger.getRESTParameters(className, method);

        // Then
        // Verify method was called without exceptions
        assertDoesNotThrow(() -> serviceCallLogger.getRESTParameters(className, method));
    }

    @Test
    void testHandleErrorWithServiceException() {
        // Given
        ServiceException exception = new ServiceException("Test service exception");
        serviceCallLogger.init();

        // When
        Message result = serviceCallLogger.handleError(exception);

        // Then
        assertNotNull(result);
        assertEquals(MessageLevel.ERROR, result.getLevel());
        assertTrue(result.getText().contains("Test service exception"));
    }

    @Test
    void testHandleErrorWithAuthorizationException() {
        // Given
        AuthorizationException exception = new AuthorizationException("Unauthorized access");
        serviceCallLogger.init();

        // When & Then
        assertThrows(AuthorizationException.class,
            () -> serviceCallLogger.handleError(exception));
    }

    @Test
    void testHandleErrorWithResourceException() {
        // Given
        ResourceException exception = new ResourceException("Resource not found", HttpStatus.NOT_FOUND);
        serviceCallLogger.init();

        // When
        Message result = serviceCallLogger.handleError(exception);

        // Then
        assertNotNull(result);
        assertEquals(MessageLevel.ERROR, result.getLevel());
        assertTrue(result.getText().contains("Resource not found"));
    }

    @Test
    void testHandleErrorWithGenericException() {
        // Given
        RuntimeException exception = new RuntimeException("Generic runtime exception");
        serviceCallLogger.init();

        // When
        Message result = serviceCallLogger.handleError(exception);

        // Then
        assertNotNull(result);
        assertEquals(MessageLevel.ERROR, result.getLevel());
        assertTrue(result.getText().contains("Generic runtime exception"));
    }

    @Test
    void testFinish() {
        // Given
        when(userUtil.getCurrentUser()).thenReturn(mockUser);
        when(request.getMethod()).thenReturn("GET");
        when(request.getHeaderNames()).thenReturn(java.util.Collections.emptyEnumeration());
        when(response.getHeaderNames()).thenReturn(java.util.Collections.emptyList());
        
        serviceCallLogger.init();

        // When
        serviceCallLogger.finish(request, response);

        // Then
        verify(loggingService).save(any(ServiceCallLog.class));
    }

    @Test
    void testConstructorInjection() {
        // Given & When
        ServiceCallLogger logger = new ServiceCallLogger(loggingService, userUtil);

        // Then
        assertNotNull(logger);
        // Verify that the logger was created successfully
        assertDoesNotThrow(() -> logger.init());
    }

    // Test controller class for testing method annotations
    public static class TestController {
        public void testMethod() {
            // Test method without annotations
        }

        @NoLogResponse
        public void testMethodWithNoLogResponse() {
            // Test method with NoLogResponse annotation
        }
    }
}
